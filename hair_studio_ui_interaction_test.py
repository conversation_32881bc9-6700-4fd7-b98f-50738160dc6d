#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Hair Studio UI Interaction Test

This script simulates core user interactions with the Hair Studio UI:
1. Drag-drop from asset library to component list
2. Component deletion from component list
3. Verification of UI state changes and data consistency
"""

import sys
import os
import logging
import time
import json
from unittest.mock import Mock, patch

# Add project path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def setup_test_environment():
    """Setup test environment and logging"""
    # Set environment variables
    os.environ["CGAME_AVATAR_FACTORY_RESOURCE"] = os.path.join(
        project_root, "cgame_avatar_factory", "resources"
    )
    os.environ["THM_LOG_LEVEL"] = "DEBUG"

    # Setup logging
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    # Setup mock modules
    import types

    maya_app = types.ModuleType("maya.app")
    maya_app.general = types.ModuleType("maya.app.general")
    maya_app.general.mayaMixin = types.ModuleType("maya.app.general.mayaMixin")

    class MayaQWidgetDockableMixin:
        def setDockableParameters(self, dockable):
            pass

    maya_app.general.mayaMixin.MayaQWidgetDockableMixin = MayaQWidgetDockableMixin
    sys.modules["maya.app"] = maya_app
    sys.modules["maya.app.general"] = maya_app.general
    sys.modules["maya.app.general.mayaMixin"] = maya_app.general.mayaMixin


class UIInteractionTester:
    """UI Interaction Test Framework"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.test_results = []
        self.app = None
        self.hair_studio_tab = None
        self.current_tab = None

    def setup_ui(self):
        """Setup the Hair Studio UI for testing"""
        try:
            from qtpy import QtWidgets, QtCore, QtTest
            from qtpy.QtCore import Qt, QPoint
            from cgame_avatar_factory.hair_studio import HairStudioTab

            # Create application
            self.app = QtWidgets.QApplication.instance()
            if self.app is None:
                self.app = QtWidgets.QApplication(sys.argv)

            # Create Hair Studio tab
            self.hair_studio_tab = HairStudioTab()
            self.hair_studio_tab.setWindowTitle("Hair Studio UI Interaction Test")
            self.hair_studio_tab.setGeometry(100, 100, 1200, 800)
            self.hair_studio_tab.show()

            # Process events to ensure UI is ready
            self.app.processEvents()
            time.sleep(0.5)  # Allow UI to stabilize

            # Get the current tab (default should be Card tab)
            self.current_tab = self.hair_studio_tab.currentWidget()

            self.logger.info("✓ Hair Studio UI setup completed")
            return True

        except Exception as e:
            self.logger.error(f"✗ Failed to setup UI: {e}")
            import traceback

            traceback.print_exc()
            return False

    def test_drag_drop_interaction(self):
        """Test drag-drop from asset library to component list"""
        self.logger.info("\n" + "=" * 50)
        self.logger.info("Testing Drag-Drop Interaction")
        self.logger.info("=" * 50)

        try:
            # Get UI components
            asset_library = getattr(self.current_tab, "asset_library", None)
            component_list = getattr(self.current_tab, "component_list", None)
            editor_area = getattr(self.current_tab, "editor_area", None)

            if not all([asset_library, component_list, editor_area]):
                self.logger.error("✗ Required UI components not found")
                return False

            # Step 1: Get initial state
            initial_component_count = len(component_list.get_components())
            self.logger.info(f"Initial component count: {initial_component_count}")

            # Step 2: Get an asset from the library
            hair_manager = getattr(self.current_tab, "hair_manager", None)
            if not hair_manager:
                self.logger.error("✗ Hair manager not found")
                return False

            assets = hair_manager.get_assets("card")
            if not assets:
                self.logger.error("✗ No assets available for testing")
                return False

            test_asset = assets[0]
            self.logger.info(f"Using test asset: {test_asset.get('name', 'Unknown')}")

            # Step 3: Simulate drag-drop operation
            self.logger.info("Simulating drag-drop operation...")

            # Create component data from asset (simulating the drag-drop process)
            component_data = {
                "id": test_asset["id"],
                "name": test_asset["name"],
                "type": test_asset.get("asset_type", "card"),
                "is_viewed": True,
                "asset_data": test_asset,
            }

            # Add component to list (simulating successful drop)
            component_list.add_component(component_data)
            self.app.processEvents()
            time.sleep(0.2)

            # Step 4: Verify component was added
            new_component_count = len(component_list.get_components())
            if new_component_count != initial_component_count + 1:
                self.logger.error(
                    f"✗ Component count mismatch. Expected: {initial_component_count + 1}, Got: {new_component_count}"
                )
                return False

            self.logger.info("✓ Component successfully added to list")

            # Step 5: Verify component is selected and trigger editor update
            # Manually select the component to trigger the signal
            component_list.select_component(test_asset["id"])
            self.app.processEvents()
            time.sleep(0.1)

            selected_components = component_list.get_selected_components()
            if not selected_components:
                self.logger.warning("⚠ No component selected after drag-drop")
            else:
                selected_component = selected_components[0]
                if selected_component.get("id") == test_asset["id"]:
                    self.logger.info("✓ Correct component is selected")
                else:
                    self.logger.error("✗ Wrong component selected")
                    return False

            # Step 6: Verify editor area updates
            # Manually trigger editor update (simulating signal connection)
            if hasattr(self.current_tab, "_on_component_selected"):
                # Simulate the signal connection by calling the handler directly
                self.current_tab._on_component_selected(test_asset["id"])
                self.app.processEvents()
                time.sleep(0.1)

            # Check if editor area has been updated
            if hasattr(editor_area, "current_component"):
                editor_data = editor_area.current_component
                if editor_data and editor_data.get("id") == test_asset["id"]:
                    self.logger.info(
                        "✓ Editor area updated with correct component data"
                    )
                else:
                    self.logger.warning(
                        "⚠ Editor area data may not be updated correctly"
                    )
            else:
                self.logger.info(
                    "ℹ Editor area update verification skipped (no current_component attribute)"
                )

            self.logger.info("✓ Drag-drop interaction test PASSED")
            return True

        except Exception as e:
            self.logger.error(f"✗ Drag-drop test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    def test_component_deletion(self):
        """Test component deletion from component list"""
        self.logger.info("\n" + "=" * 50)
        self.logger.info("Testing Component Deletion")
        self.logger.info("=" * 50)

        try:
            # Get UI components
            component_list = getattr(self.current_tab, "component_list", None)
            editor_area = getattr(self.current_tab, "editor_area", None)

            if not all([component_list, editor_area]):
                self.logger.error("✗ Required UI components not found")
                return False

            # Step 1: Get initial state
            initial_components = component_list.get_components()
            if not initial_components:
                self.logger.error("✗ No components available for deletion test")
                return False

            initial_count = len(initial_components)
            test_component = initial_components[0]
            test_component_id = test_component.get("id")
            test_component_name = test_component.get("name", "Unknown")

            self.logger.info(f"Initial component count: {initial_count}")
            self.logger.info(f"Testing deletion of: {test_component_name}")

            # Step 2: Select the component
            component_list.select_component(test_component_id)
            self.app.processEvents()
            time.sleep(0.1)

            # Verify selection
            selected_components = component_list.get_selected_components()
            if (
                not selected_components
                or selected_components[0].get("id") != test_component_id
            ):
                self.logger.error("✗ Failed to select component for deletion")
                return False

            self.logger.info("✓ Component selected for deletion")

            # Step 3: Delete the component
            self.logger.info("Simulating component deletion...")
            component_list.remove_component(test_component_id)
            self.app.processEvents()
            time.sleep(0.2)

            # Step 4: Verify component was removed
            new_components = component_list.get_components()
            new_count = len(new_components)

            if new_count != initial_count - 1:
                self.logger.error(
                    f"✗ Component count mismatch after deletion. Expected: {initial_count - 1}, Got: {new_count}"
                )
                return False

            # Verify the specific component was removed
            remaining_ids = [comp.get("id") for comp in new_components]
            if test_component_id in remaining_ids:
                self.logger.error("✗ Component still exists after deletion")
                return False

            self.logger.info("✓ Component successfully removed from list")

            # Step 5: Verify no component is selected
            selected_components = component_list.get_selected_components()
            if selected_components:
                self.logger.warning("⚠ Components still selected after deletion")
            else:
                self.logger.info("✓ No components selected after deletion")

            # Step 6: Verify editor area is cleared
            # Manually trigger editor clear (simulating signal connection)
            if hasattr(self.current_tab, "_on_component_selected"):
                # Simulate clearing selection by passing None
                self.current_tab._on_component_selected(None)
                self.app.processEvents()
                time.sleep(0.1)

            if hasattr(editor_area, "current_component"):
                editor_data = editor_area.current_component
                if editor_data is None:
                    self.logger.info("✓ Editor area cleared after component deletion")
                else:
                    self.logger.warning("⚠ Editor area may not be cleared correctly")
            else:
                self.logger.info(
                    "ℹ Editor area clear verification skipped (no current_component attribute)"
                )

            self.logger.info("✓ Component deletion test PASSED")
            return True

        except Exception as e:
            self.logger.error(f"✗ Component deletion test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    def run_all_tests(self):
        """Run all UI interaction tests"""
        self.logger.info("=" * 60)
        self.logger.info("Hair Studio UI Interaction Test Suite")
        self.logger.info("=" * 60)

        # Setup UI
        if not self.setup_ui():
            return False

        # Run tests
        tests = [
            ("Drag-Drop Interaction", self.test_drag_drop_interaction),
            ("Component Deletion", self.test_component_deletion),
        ]

        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
                self.test_results.append(
                    {"test": test_name, "passed": result, "error": None}
                )
            except Exception as e:
                self.logger.error(f"✗ {test_name} crashed: {e}")
                results.append((test_name, False))
                self.test_results.append(
                    {"test": test_name, "passed": False, "error": str(e)}
                )

        # Generate summary
        self.generate_test_summary(results)

        # Cleanup
        if self.hair_studio_tab:
            self.hair_studio_tab.close()

        return all(result for _, result in results)

    def generate_test_summary(self, results):
        """Generate test summary report"""
        self.logger.info("\n" + "=" * 60)
        self.logger.info("TEST SUMMARY REPORT")
        self.logger.info("=" * 60)

        passed = sum(1 for _, result in results if result)
        total = len(results)

        for test_name, result in results:
            status = "✓ PASSED" if result else "✗ FAILED"
            self.logger.info(f"{test_name:.<40} {status}")

        self.logger.info(f"\nOverall Result: {passed}/{total} tests passed")

        if passed == total:
            self.logger.info("\n🎉 ALL TESTS PASSED!")
            self.logger.info("Hair Studio UI interactions are working correctly.")
        else:
            self.logger.info(f"\n⚠ {total - passed} test(s) failed.")
            self.logger.info("Please review the test output above for details.")


def main():
    """Main test function"""
    setup_test_environment()

    tester = UIInteractionTester()

    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        return 1
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
