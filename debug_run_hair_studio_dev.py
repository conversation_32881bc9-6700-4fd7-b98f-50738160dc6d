#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
毛发工作室开发测试启动器
使用Maya环境但不启动Maya GUI，只运行毛发工作室UI
"""

import sys
import os
import logging

# 添加项目路径到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def setup_mock_modules():
    """设置必要的Mock模块，避免Maya GUI相关的依赖"""
    import types

    # Mock maya.app.general.mayaMixin - 避免Maya GUI依赖
    maya_app = types.ModuleType("maya.app")
    maya_app.general = types.ModuleType("maya.app.general")
    maya_app.general.mayaMixin = types.ModuleType("maya.app.general.mayaMixin")

    class MayaQWidgetDockableMixin:
        def setDockableParameters(self, dockable):
            pass

    maya_app.general.mayaMixin.MayaQWidgetDockableMixin = MayaQWidgetDockableMixin

    sys.modules["maya.app"] = maya_app
    sys.modules["maya.app.general"] = maya_app.general
    sys.modules["maya.app.general.mayaMixin"] = maya_app.general.mayaMixin

    # Mock lightbox_ui.banner - 避免banner依赖
    lightbox_ui = sys.modules.get("lightbox_ui")
    if lightbox_ui is None:
        lightbox_ui = types.ModuleType("lightbox_ui")
        sys.modules["lightbox_ui"] = lightbox_ui

    if not hasattr(lightbox_ui, "banner"):
        lightbox_ui.banner = types.ModuleType("lightbox_ui.banner")
        lightbox_ui.banner.setup_banner = lambda *args, **kwargs: None
        sys.modules["lightbox_ui.banner"] = lightbox_ui.banner


def setup_environment():
    """设置开发环境变量"""
    # 设置资源路径
    os.environ["CGAME_AVATAR_FACTORY_RESOURCE"] = os.path.join(
        project_root, "cgame_avatar_factory", "resources"
    )
    os.environ["THM_LOG_LEVEL"] = "DEBUG"

    # 设置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )


def run_hair_studio_dev():
    """运行毛发工作室开发环境"""
    print("=" * 60)
    print("毛发工作室开发测试环境")
    print("=" * 60)
    print("正在启动...")

    # 设置环境
    setup_environment()
    setup_mock_modules()

    print("✓ 环境设置完成")

    try:
        # 检查Maya环境
        try:
            import maya.cmds as cmds

            print("✓ Maya环境检测成功")
            maya_available = True
        except ImportError:
            print("✗ Maya环境不可用")
            maya_available = False

        # 检查Qt库
        try:
            from qtpy import QtWidgets, QtCore

            print("✓ Qt库（qtpy）可用")
        except ImportError:
            try:
                from PySide2 import QtWidgets, QtCore

                print("✓ Qt库（PySide2）可用")
                # 创建qtpy兼容层
                import types

                qtpy = types.ModuleType("qtpy")
                qtpy.QtWidgets = QtWidgets
                qtpy.QtCore = QtCore
                sys.modules["qtpy"] = qtpy
            except ImportError:
                print("✗ 没有可用的Qt库")
                return False

        # 检查dayu_widgets
        try:
            from dayu_widgets import dayu_theme
            from dayu_widgets.qt import application

            print("✓ dayu_widgets可用")
            use_dayu = True
        except ImportError:
            print("⚠ dayu_widgets不可用，使用基本Qt")
            use_dayu = False

        # 导入毛发工作室模块
        try:
            from cgame_avatar_factory.hair_studio import HairStudioTab

            print("✓ 毛发工作室模块导入成功")
        except Exception as e:
            print(f"✗ 毛发工作室模块导入失败: {e}")
            print("详细错误信息:")
            import traceback

            traceback.print_exc()

            # 尝试逐步导入来定位问题
            print("\n尝试逐步导入来定位问题...")
            try:
                from cgame_avatar_factory.hair_studio.ui import hair_studio_tab

                print("✓ hair_studio_tab 模块导入成功")
            except Exception as e2:
                print(f"✗ hair_studio_tab 模块导入失败: {e2}")

            try:
                from cgame_avatar_factory.hair_studio.ui.base_hair_tab import (
                    BaseHairTab,
                )

                print("✓ BaseHairTab 导入成功")
            except Exception as e3:
                print(f"✗ BaseHairTab 导入失败: {e3}")

            try:
                from cgame_avatar_factory.hair_studio.ui.component_list import (
                    ComponentList,
                )

                print("✓ ComponentList 导入成功")
            except Exception as e4:
                print(f"✗ ComponentList 导入失败: {e4}")

            try:
                from cgame_avatar_factory.hair_studio.ui.component_item import (
                    ComponentItem,
                )

                print("✓ ComponentItem 导入成功")
            except Exception as e5:
                print(f"✗ ComponentItem 导入失败: {e5}")

            return False

        print("\n" + "=" * 60)
        print("启动毛发工作室UI...")
        print("=" * 60)

        # 创建应用程序
        if use_dayu:
            # 使用dayu_widgets应用
            with application():
                # 设置主题
                dayu_theme.set_theme("dark")
                dayu_theme.set_primary_color(dayu_theme.blue)

                # 创建主窗口
                main_window = QtWidgets.QMainWindow()
                main_window.setWindowTitle("毛发工作室 - 开发测试环境")
                main_window.setGeometry(100, 100, 1400, 900)

                # 创建毛发工作室tab
                hair_studio_tab = HairStudioTab()

                # 应用主题
                dayu_theme.apply(main_window)
                dayu_theme.apply(hair_studio_tab)

                # 设置中央widget
                main_window.setCentralWidget(hair_studio_tab)

                # 显示窗口
                main_window.show()

                print("✓ 毛发工作室UI启动成功！")
                print("✓ 使用dayu_widgets主题")
                print("✓ 窗口已显示，可以进行开发测试")
                print("\n按Ctrl+C退出...")

        else:
            # 使用基本Qt应用
            app = QtWidgets.QApplication(sys.argv)

            # 创建主窗口
            main_window = QtWidgets.QMainWindow()
            main_window.setWindowTitle("毛发工作室 - 开发测试环境（基本模式）")
            main_window.setGeometry(100, 100, 1400, 900)

            # 创建毛发工作室tab
            hair_studio_tab = HairStudioTab()

            # 设置中央widget
            main_window.setCentralWidget(hair_studio_tab)

            # 显示窗口
            main_window.show()

            print("✓ 毛发工作室UI启动成功！")
            print("✓ 使用基本Qt模式")
            print("✓ 窗口已显示，可以进行开发测试")

            # 运行应用
            app.exec_()

        return True

    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
        return True
    except Exception as e:
        print(f"\n✗ 启动失败: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_hair_studio_dev()
    if success:
        print("\n" + "=" * 60)
        print("毛发工作室开发环境已退出")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("启动失败，请检查错误信息")
        print("=" * 60)
        sys.exit(1)
