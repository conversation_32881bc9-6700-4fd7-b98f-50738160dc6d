"""Test cases for Hair Studio data layer fixes.

This module contains test cases to verify that the data layer fixes are working correctly
without requiring Qt dependencies.
"""

import unittest
import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager


class TestDataFixes(unittest.TestCase):
    """Test cases for data layer fixes."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_manager = MockDataManager()
    
    def test_initial_component_list_empty(self):
        """Test that initial component list is empty."""
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 0, "Initial component list should be empty")
        print("✅ Initial component list is empty")
    
    def test_asset_library_has_assets(self):
        """Test that asset library has sample assets."""
        assets = self.mock_manager.get_assets()
        self.assertGreater(len(assets), 0, "Asset library should have sample assets")
        
        # Test filtering by type
        card_assets = self.mock_manager.get_assets("card")
        xgen_assets = self.mock_manager.get_assets("xgen")
        curve_assets = self.mock_manager.get_assets("curve")
        
        self.assertGreater(len(card_assets), 0, "Should have card assets")
        self.assertGreater(len(xgen_assets), 0, "Should have xgen assets")
        self.assertGreater(len(curve_assets), 0, "Should have curve assets")
        
        print(f"✅ Asset library has {len(assets)} total assets")
        print(f"   - Card assets: {len(card_assets)}")
        print(f"   - XGen assets: {len(xgen_assets)}")
        print(f"   - Curve assets: {len(curve_assets)}")
    
    def test_component_creation_from_asset(self):
        """Test component creation from asset."""
        # Get a sample asset
        assets = self.mock_manager.get_assets("card")
        self.assertGreater(len(assets), 0, "Need at least one card asset for testing")
        
        asset = assets[0]
        asset_id = asset["id"]
        
        # Create component
        component = self.mock_manager.create_component(asset_id)
        self.assertIsNotNone(component, "Component should be created successfully")
        self.assertEqual(component["type"], "card", "Component type should match asset type")
        self.assertEqual(component["asset_id"], asset_id, "Component should reference the asset")
        
        # Verify component is in the list
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 1, "Should have one component after creation")
        self.assertEqual(components[0]["id"], component["id"], "Component should be in the list")
        
        print(f"✅ Component created successfully: {component['name']}")
    
    def test_component_has_is_viewed_property(self):
        """Test that components have is_viewed property."""
        # Create a component
        assets = self.mock_manager.get_assets("card")
        asset = assets[0]
        component = self.mock_manager.create_component(asset["id"])
        
        # Check is_viewed property
        self.assertIn("is_viewed", component, "Component should have is_viewed property")
        self.assertTrue(component["is_viewed"], "Component should be viewed by default")
        
        print("✅ Component has is_viewed property (default: True)")
    
    def test_component_visibility_toggle(self):
        """Test component visibility toggle functionality."""
        # Create a component
        assets = self.mock_manager.get_assets("card")
        asset = assets[0]
        component = self.mock_manager.create_component(asset["id"])
        component_id = component["id"]
        
        # Test toggling is_viewed
        success = self.mock_manager.update_component(component_id, is_viewed=False)
        self.assertTrue(success, "Should be able to update is_viewed property")
        
        # Verify the change
        updated_component = self.mock_manager.get_component(component_id)
        self.assertFalse(updated_component["is_viewed"], "is_viewed should be False after update")
        
        # Toggle back
        success = self.mock_manager.update_component(component_id, is_viewed=True)
        self.assertTrue(success, "Should be able to toggle is_viewed back")
        
        updated_component = self.mock_manager.get_component(component_id)
        self.assertTrue(updated_component["is_viewed"], "is_viewed should be True after toggle back")
        
        print("✅ Component visibility toggle works correctly")
    
    def test_component_deletion(self):
        """Test component deletion."""
        # Create a component
        assets = self.mock_manager.get_assets("card")
        asset = assets[0]
        component = self.mock_manager.create_component(asset["id"])
        component_id = component["id"]
        
        # Verify it exists
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 1, "Should have one component")
        
        # Delete it
        success = self.mock_manager.delete_component(component_id)
        self.assertTrue(success, "Component deletion should succeed")
        
        # Verify it's gone
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 0, "Component list should be empty after deletion")
        
        deleted_component = self.mock_manager.get_component(component_id)
        self.assertIsNone(deleted_component, "Deleted component should not be found")
        
        print("✅ Component deletion works correctly")
    
    def test_multiple_component_types(self):
        """Test creating components of different types."""
        # Create components of each type
        card_assets = self.mock_manager.get_assets("card")
        xgen_assets = self.mock_manager.get_assets("xgen")
        curve_assets = self.mock_manager.get_assets("curve")
        
        card_component = self.mock_manager.create_component(card_assets[0]["id"])
        xgen_component = self.mock_manager.create_component(xgen_assets[0]["id"])
        curve_component = self.mock_manager.create_component(curve_assets[0]["id"])
        
        # Verify all were created
        self.assertIsNotNone(card_component, "Card component should be created")
        self.assertIsNotNone(xgen_component, "XGen component should be created")
        self.assertIsNotNone(curve_component, "Curve component should be created")
        
        # Verify types
        self.assertEqual(card_component["type"], "card")
        self.assertEqual(xgen_component["type"], "xgen")
        self.assertEqual(curve_component["type"], "curve")
        
        # Verify total count
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 3, "Should have three components total")
        
        # Test filtering by type
        card_components = self.mock_manager.get_components("card")
        xgen_components = self.mock_manager.get_components("xgen")
        curve_components = self.mock_manager.get_components("curve")
        
        self.assertEqual(len(card_components), 1, "Should have one card component")
        self.assertEqual(len(xgen_components), 1, "Should have one xgen component")
        self.assertEqual(len(curve_components), 1, "Should have one curve component")
        
        print("✅ Multiple component types work correctly")
        print(f"   - Created {len(components)} components total")


def run_tests():
    """Run all tests and return results."""
    print("Running Hair Studio Data Layer Fix Tests...")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestDataFixes)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=0)  # Reduce verbosity since we have custom prints
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    
    print("=" * 60)
    if success:
        print("🎉 All data layer tests passed! Core fixes are working correctly.")
        print("\nKey fixes verified:")
        print("1. ✅ Initial component list is empty (no pre-created data)")
        print("2. ✅ Asset library has sample assets for testing")
        print("3. ✅ Component creation from assets works")
        print("4. ✅ Components have is_viewed property for UI toggle")
        print("5. ✅ Component visibility toggle functionality works")
        print("6. ✅ Component deletion works correctly")
        print("7. ✅ Multiple component types (card/xgen/curve) supported")
    else:
        print("❌ Some tests failed. Please check the output above.")
    
    print("\nNote: UI layer tests require Maya/Qt environment.")
    print("Use debug_start_hair_dev_direct.cmd for full UI testing.")
    
    sys.exit(0 if success else 1)
