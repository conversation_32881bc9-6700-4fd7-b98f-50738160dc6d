"""Test cases for Hair Studio UI fixes.

This module contains test cases to verify that the UI fixes are working correctly:
1. Initial component list should be empty
2. Asset library should support highlighting selected assets
3. Click should only highlight, drag-drop should create components
4. Component list should show icons and visibility toggle
"""

import unittest
import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager


class TestUIFixes(unittest.TestCase):
    """Test cases for UI fixes."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_manager = MockDataManager()
        self.hair_manager = HairManager()
    
    def test_initial_component_list_empty(self):
        """Test that initial component list is empty."""
        # Test MockDataManager
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 0, "Initial component list should be empty")
        
        # Test HairManager
        hair_components = self.hair_manager.get_components()
        self.assertEqual(len(hair_components), 0, "Initial hair manager component list should be empty")
    
    def test_asset_library_has_assets(self):
        """Test that asset library has sample assets."""
        assets = self.mock_manager.get_assets()
        self.assertGreater(len(assets), 0, "Asset library should have sample assets")
        
        # Test filtering by type
        card_assets = self.mock_manager.get_assets("card")
        xgen_assets = self.mock_manager.get_assets("xgen")
        curve_assets = self.mock_manager.get_assets("curve")
        
        self.assertGreater(len(card_assets), 0, "Should have card assets")
        self.assertGreater(len(xgen_assets), 0, "Should have xgen assets")
        self.assertGreater(len(curve_assets), 0, "Should have curve assets")
    
    def test_component_creation_from_asset(self):
        """Test component creation from asset."""
        # Get a sample asset
        assets = self.mock_manager.get_assets("card")
        self.assertGreater(len(assets), 0, "Need at least one card asset for testing")
        
        asset = assets[0]
        asset_id = asset["id"]
        
        # Create component
        component = self.mock_manager.create_component(asset_id)
        self.assertIsNotNone(component, "Component should be created successfully")
        self.assertEqual(component["type"], "card", "Component type should match asset type")
        self.assertEqual(component["asset_id"], asset_id, "Component should reference the asset")
        
        # Verify component is in the list
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 1, "Should have one component after creation")
        self.assertEqual(components[0]["id"], component["id"], "Component should be in the list")
    
    def test_component_has_is_viewed_property(self):
        """Test that components have is_viewed property."""
        # Create a component
        assets = self.mock_manager.get_assets("card")
        asset = assets[0]
        component = self.mock_manager.create_component(asset["id"])
        
        # Check is_viewed property
        self.assertIn("is_viewed", component, "Component should have is_viewed property")
        self.assertTrue(component["is_viewed"], "Component should be viewed by default")
    
    def test_component_visibility_toggle(self):
        """Test component visibility toggle functionality."""
        # Create a component
        assets = self.mock_manager.get_assets("card")
        asset = assets[0]
        component = self.mock_manager.create_component(asset["id"])
        component_id = component["id"]
        
        # Test toggling is_viewed
        success = self.mock_manager.update_component(component_id, is_viewed=False)
        self.assertTrue(success, "Should be able to update is_viewed property")
        
        # Verify the change
        updated_component = self.mock_manager.get_component(component_id)
        self.assertFalse(updated_component["is_viewed"], "is_viewed should be False after update")
        
        # Toggle back
        success = self.mock_manager.update_component(component_id, is_viewed=True)
        self.assertTrue(success, "Should be able to toggle is_viewed back")
        
        updated_component = self.mock_manager.get_component(component_id)
        self.assertTrue(updated_component["is_viewed"], "is_viewed should be True after toggle back")
    
    def test_component_deletion(self):
        """Test component deletion."""
        # Create a component
        assets = self.mock_manager.get_assets("card")
        asset = assets[0]
        component = self.mock_manager.create_component(asset["id"])
        component_id = component["id"]
        
        # Verify it exists
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 1, "Should have one component")
        
        # Delete it
        success = self.mock_manager.delete_component(component_id)
        self.assertTrue(success, "Component deletion should succeed")
        
        # Verify it's gone
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 0, "Component list should be empty after deletion")
        
        deleted_component = self.mock_manager.get_component(component_id)
        self.assertIsNone(deleted_component, "Deleted component should not be found")
    
    def test_multiple_component_types(self):
        """Test creating components of different types."""
        # Create components of each type
        card_assets = self.mock_manager.get_assets("card")
        xgen_assets = self.mock_manager.get_assets("xgen")
        curve_assets = self.mock_manager.get_assets("curve")
        
        card_component = self.mock_manager.create_component(card_assets[0]["id"])
        xgen_component = self.mock_manager.create_component(xgen_assets[0]["id"])
        curve_component = self.mock_manager.create_component(curve_assets[0]["id"])
        
        # Verify all were created
        self.assertIsNotNone(card_component, "Card component should be created")
        self.assertIsNotNone(xgen_component, "XGen component should be created")
        self.assertIsNotNone(curve_component, "Curve component should be created")
        
        # Verify types
        self.assertEqual(card_component["type"], "card")
        self.assertEqual(xgen_component["type"], "xgen")
        self.assertEqual(curve_component["type"], "curve")
        
        # Verify total count
        components = self.mock_manager.get_components()
        self.assertEqual(len(components), 3, "Should have three components total")
        
        # Test filtering by type
        card_components = self.mock_manager.get_components("card")
        xgen_components = self.mock_manager.get_components("xgen")
        curve_components = self.mock_manager.get_components("curve")
        
        self.assertEqual(len(card_components), 1, "Should have one card component")
        self.assertEqual(len(xgen_components), 1, "Should have one xgen component")
        self.assertEqual(len(curve_components), 1, "Should have one curve component")


def run_tests():
    """Run all tests and return results."""
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestUIFixes)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    print("Running Hair Studio UI Fix Tests...")
    print("=" * 50)
    
    success = run_tests()
    
    print("=" * 50)
    if success:
        print("✅ All tests passed! UI fixes are working correctly.")
    else:
        print("❌ Some tests failed. Please check the output above.")
    
    sys.exit(0 if success else 1)
