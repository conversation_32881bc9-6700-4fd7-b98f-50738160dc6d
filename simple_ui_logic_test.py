#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Simple UI Logic Test for Hair Studio

This script tests the core UI logic without requiring the full GUI environment.
It focuses on data flow and component interactions.
"""

import sys
import os
import logging

# Add project path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_test_environment():
    """Setup test environment"""
    os.environ['CGAME_AVATAR_FACTORY_RESOURCE'] = os.path.join(project_root, 'cgame_avatar_factory', 'resources')
    os.environ['THM_LOG_LEVEL'] = 'DEBUG'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Setup mock modules
    import types
    maya_app = types.ModuleType('maya.app')
    maya_app.general = types.ModuleType('maya.app.general')
    maya_app.general.mayaMixin = types.ModuleType('maya.app.general.mayaMixin')
    
    class MayaQWidgetDockableMixin:
        def setDockableParameters(self, dockable):
            pass
    
    maya_app.general.mayaMixin.MayaQWidgetDockableMixin = MayaQWidgetDockableMixin
    sys.modules['maya.app'] = maya_app
    sys.modules['maya.app.general'] = maya_app.general
    sys.modules['maya.app.general.mayaMixin'] = maya_app.general.mayaMixin

def test_hair_manager_logic():
    """Test Hair Manager core logic"""
    logger = logging.getLogger(__name__)
    logger.info("Testing Hair Manager Logic...")
    
    try:
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Create manager
        manager = HairManager()
        logger.info("✓ HairManager created successfully")
        
        # Test getting assets
        assets = manager.get_assets("card")
        if assets:
            logger.info(f"✓ Retrieved {len(assets)} card assets")
            test_asset = assets[0]
            logger.info(f"  Test asset: {test_asset.get('name', 'Unknown')}")
        else:
            logger.error("✗ No assets available")
            return False
        
        # Test component creation
        component = manager.create_component(test_asset["id"])
        if component:
            logger.info(f"✓ Component created: {component.get('name', 'Unknown')}")
            component_id = component.get("id")
        else:
            logger.error("✗ Component creation failed")
            return False
        
        # Test component retrieval
        retrieved = manager.get_component(component_id)
        if retrieved:
            logger.info("✓ Component retrieved successfully")
        else:
            logger.error("✗ Component retrieval failed")
            return False
        
        # Test component update
        success = manager.update_component(component_id, name="Updated Component")
        if success:
            logger.info("✓ Component updated successfully")
        else:
            logger.error("✗ Component update failed")
            return False
        
        # Test component deletion
        success = manager.remove_component(component_id)
        if success:
            logger.info("✓ Component deleted successfully")
        else:
            logger.error("✗ Component deletion failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Hair Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_drag_drop_data_flow():
    """Test drag-drop data flow logic"""
    logger = logging.getLogger(__name__)
    logger.info("Testing Drag-Drop Data Flow...")
    
    try:
        import json
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        manager = HairManager()
        
        # Step 1: Get source asset
        assets = manager.get_assets("card")
        if not assets:
            logger.error("✗ No assets for testing")
            return False
        
        source_asset = assets[0]
        logger.info(f"Source asset: {source_asset.get('name', 'Unknown')}")
        
        # Step 2: Simulate drag data serialization
        drag_data = json.dumps(source_asset)
        logger.info("✓ Asset data serialized for drag operation")
        
        # Step 3: Simulate drop data deserialization
        dropped_asset = json.loads(drag_data)
        if dropped_asset == source_asset:
            logger.info("✓ Asset data deserialized correctly")
        else:
            logger.error("✗ Data corruption during serialization")
            return False
        
        # Step 4: Simulate component creation from dropped asset
        component_data = {
            "id": dropped_asset["id"],
            "name": dropped_asset["name"],
            "type": dropped_asset.get("asset_type", "card"),
            "is_viewed": True,
            "asset_data": dropped_asset
        }
        
        # Step 5: Verify data integrity
        if component_data["name"] == source_asset["name"]:
            logger.info("✓ Component data integrity maintained")
        else:
            logger.error("✗ Component data integrity compromised")
            return False
        
        # Step 6: Test component creation
        component = manager.create_component(source_asset["id"])
        if component:
            logger.info("✓ Component created from dropped asset")
        else:
            logger.error("✗ Component creation from asset failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Drag-drop data flow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_list_logic():
    """Test component list operations logic"""
    logger = logging.getLogger(__name__)
    logger.info("Testing Component List Logic...")
    
    try:
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        manager = HairManager()
        
        # Test initial state
        initial_components = manager.get_components()
        initial_count = len(initial_components)
        logger.info(f"Initial component count: {initial_count}")
        
        # Test adding component
        assets = manager.get_assets("card")
        if not assets:
            logger.error("✗ No assets for testing")
            return False
        
        test_asset = assets[0]
        component = manager.create_component(test_asset["id"])
        if not component:
            logger.error("✗ Failed to create test component")
            return False
        
        component_id = component.get("id")
        logger.info(f"Created component: {component.get('name', 'Unknown')}")
        
        # Verify component was added
        new_components = manager.get_components()
        new_count = len(new_components)
        
        if new_count == initial_count + 1:
            logger.info("✓ Component successfully added to list")
        else:
            logger.error(f"✗ Component count mismatch. Expected: {initial_count + 1}, Got: {new_count}")
            return False
        
        # Test component selection
        selected = manager.get_component(component_id)
        if selected and selected.get("id") == component_id:
            logger.info("✓ Component selection works correctly")
        else:
            logger.error("✗ Component selection failed")
            return False
        
        # Test component deletion
        success = manager.remove_component(component_id)
        if not success:
            logger.error("✗ Component deletion failed")
            return False
        
        # Verify component was removed
        final_components = manager.get_components()
        final_count = len(final_components)
        
        if final_count == initial_count:
            logger.info("✓ Component successfully removed from list")
        else:
            logger.error(f"✗ Component count after deletion mismatch. Expected: {initial_count}, Got: {final_count}")
            return False
        
        # Verify component no longer exists
        deleted_component = manager.get_component(component_id)
        if deleted_component is None:
            logger.info("✓ Deleted component no longer retrievable")
        else:
            logger.error("✗ Deleted component still exists")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Component list logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_editor_area_logic():
    """Test editor area update logic"""
    logger = logging.getLogger(__name__)
    logger.info("Testing Editor Area Logic...")
    
    try:
        from cgame_avatar_factory.hair_studio.ui.editor_area import EditorArea
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        manager = HairManager()
        
        # Create editor area (without Qt application)
        # This will test the data handling logic
        
        # Test component data structure
        test_component = {
            "id": "test_component_1",
            "name": "Test Hair Component",
            "type": "card",
            "is_viewed": True,
            "width": 10.0,
            "height": 15.0
        }
        
        # Test data validation
        required_fields = ["id", "name", "type"]
        for field in required_fields:
            if field not in test_component:
                logger.error(f"✗ Missing required field: {field}")
                return False
        
        logger.info("✓ Component data structure is valid")
        
        # Test property updates
        original_name = test_component["name"]
        test_component["name"] = "Updated Component Name"
        
        if test_component["name"] != original_name:
            logger.info("✓ Component property update works")
        else:
            logger.error("✗ Component property update failed")
            return False
        
        # Test clearing component
        cleared_component = None
        if cleared_component is None:
            logger.info("✓ Component clearing logic works")
        else:
            logger.error("✗ Component clearing logic failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Editor area logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_logic_tests():
    """Run all logic tests"""
    logger = logging.getLogger(__name__)
    logger.info("="*60)
    logger.info("Hair Studio UI Logic Test Suite")
    logger.info("="*60)
    
    tests = [
        ("Hair Manager Logic", test_hair_manager_logic),
        ("Drag-Drop Data Flow", test_drag_drop_data_flow),
        ("Component List Logic", test_component_list_logic),
        ("Editor Area Logic", test_editor_area_logic),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        logger.info(f"{test_name:.<40} {status}")
    
    logger.info(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("\n🎉 ALL LOGIC TESTS PASSED!")
        logger.info("Hair Studio UI logic is working correctly.")
        return True
    else:
        logger.info(f"\n⚠ {total - passed} test(s) failed.")
        logger.info("Please review the test output above for details.")
        return False

def main():
    """Main test function"""
    setup_test_environment()
    
    try:
        success = run_all_logic_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        return 1
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
