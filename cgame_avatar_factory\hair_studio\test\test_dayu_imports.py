#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Test script to check dayu_widgets imports"""

import sys
import os

# Add project path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_dayu_imports():
    """Test what can be imported from dayu_widgets"""
    print("Testing dayu_widgets imports...")
    
    try:
        import dayu_widgets
        print("✓ dayu_widgets imported successfully")
        print("Available in dayu_widgets:", dir(dayu_widgets))
        
        # Test specific imports
        imports_to_test = [
            'MLabel',
            'MIcon', 
            'MPushButton',
            'MLineEdit',
            'MTabWidget',
            'MWidget',
            'MSplitter',
            'MScrollArea',
            'MGridLayout',
            'MHBoxLayout',
            'MVBoxLayout'
        ]
        
        available_imports = []
        missing_imports = []
        
        for import_name in imports_to_test:
            try:
                obj = getattr(dayu_widgets, import_name)
                available_imports.append(import_name)
                print(f"✓ {import_name} available")
            except AttributeError:
                missing_imports.append(import_name)
                print(f"✗ {import_name} NOT available")
        
        print(f"\nSummary:")
        print(f"Available: {available_imports}")
        print(f"Missing: {missing_imports}")
        
        # Try alternative imports
        print(f"\nTrying alternative imports...")
       
        try:
            from dayu_widgets.qt import MIcon as QtMIcon
            print("✓ MIcon available via dayu_widgets.qt")
        except ImportError:
            print("✗ MIcon not available via dayu_widgets.qt")
            
        # Check version
        try:
            version = getattr(dayu_widgets, '__version__', 'Unknown')
            print(f"dayu_widgets version: {version}")
        except:
            print("Could not determine dayu_widgets version")
            
    except ImportError as e:
        print(f"✗ Failed to import dayu_widgets: {e}")
        return False
        
    return True

if __name__ == "__main__":
    test_dayu_imports()
