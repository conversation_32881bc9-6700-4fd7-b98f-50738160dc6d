#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Comprehensive Hair Studio UI and Drag-Drop Test

This script performs comprehensive testing of Hair Studio UI functionality
and drag-drop operations to verify everything works correctly.
"""

import sys
import os
import logging
import traceback
import time
from unittest.mock import Mock, patch

# Add project path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_logging():
    """Setup logging for the test system"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def setup_mock_modules():
    """Setup necessary mock modules"""
    import types
    
    # Mock maya.app.general.mayaMixin
    maya_app = types.ModuleType('maya.app')
    maya_app.general = types.ModuleType('maya.app.general')
    maya_app.general.mayaMixin = types.ModuleType('maya.app.general.mayaMixin')
    
    class MayaQWidgetDockableMixin:
        def setDockableParameters(self, dockable):
            pass
    
    maya_app.general.mayaMixin.MayaQWidgetDockableMixin = MayaQWidgetDockableMixin
    
    sys.modules['maya.app'] = maya_app
    sys.modules['maya.app.general'] = maya_app.general
    sys.modules['maya.app.general.mayaMixin'] = maya_app.general.mayaMixin

def test_ui_components():
    """Test all UI components individually"""
    logger = logging.getLogger(__name__)
    logger.info("Testing individual UI components...")
    
    try:
        from qtpy import QtWidgets, QtCore, QtTest
        from qtpy.QtCore import Qt, QPoint
        
        # Create application
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Test ComponentItem
        logger.info("Testing ComponentItem...")
        from cgame_avatar_factory.hair_studio.ui.component_item import ComponentItem
        
        test_component_data = {
            "id": "test_component_1",
            "name": "Test Hair Component",
            "type": "card",
            "is_viewed": True
        }
        
        component_item = ComponentItem(test_component_data)
        component_item.show()
        app.processEvents()
        
        # Test visibility toggle
        component_item._on_visibility_clicked()
        assert component_item.component_data["is_viewed"] == False
        logger.info("✓ ComponentItem visibility toggle works")
        
        # Test selection
        component_item.set_selected(True)
        assert component_item.is_selected() == True
        logger.info("✓ ComponentItem selection works")
        
        component_item.close()
        
        # Test ComponentList
        logger.info("Testing ComponentList...")
        from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        hair_manager = HairManager()
        component_list = ComponentList(hair_manager=hair_manager)
        component_list.show()
        app.processEvents()
        
        # Test adding component
        component_list.add_component(test_component_data)
        assert len(component_list.get_components()) == 1
        logger.info("✓ ComponentList add component works")
        
        # Test removing component
        component_list.remove_component("test_component_1")
        assert len(component_list.get_components()) == 0
        logger.info("✓ ComponentList remove component works")
        
        component_list.close()
        
        # Test AssetLibrary
        logger.info("Testing AssetLibrary...")
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
        
        asset_library = AssetLibrary(hair_manager=hair_manager)
        asset_library.show()
        app.processEvents()
        
        # Test asset loading
        asset_library.load_assets("card")
        app.processEvents()
        logger.info("✓ AssetLibrary load assets works")
        
        asset_library.close()
        
        logger.info("✓ All UI components tested successfully")
        return True
        
    except Exception as e:
        logger.error(f"✗ UI component test failed: {e}")
        logger.error(traceback.format_exc())
        return False

def test_hair_studio_tab():
    """Test the main HairStudioTab functionality"""
    logger = logging.getLogger(__name__)
    logger.info("Testing HairStudioTab functionality...")
    
    try:
        from qtpy import QtWidgets, QtCore, QtTest
        from cgame_avatar_factory.hair_studio import HairStudioTab
        
        # Create application
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Create HairStudioTab
        hair_studio_tab = HairStudioTab()
        hair_studio_tab.show()
        app.processEvents()
        
        logger.info(f"HairStudioTab created with {hair_studio_tab.count()} tabs")
        
        # Test tab switching
        for i in range(hair_studio_tab.count()):
            hair_studio_tab.setCurrentIndex(i)
            app.processEvents()
            current_tab = hair_studio_tab.currentWidget()
            tab_text = hair_studio_tab.tabText(i)
            logger.info(f"✓ Switched to tab {i}: {tab_text}")
            
            # Test that each tab has the required components
            if hasattr(current_tab, 'asset_library'):
                logger.info(f"  ✓ Tab {i} has asset_library")
            if hasattr(current_tab, 'component_list'):
                logger.info(f"  ✓ Tab {i} has component_list")
            if hasattr(current_tab, 'editor_area'):
                logger.info(f"  ✓ Tab {i} has editor_area")
        
        # Test resizing
        hair_studio_tab.resize(1200, 800)
        app.processEvents()
        logger.info("✓ HairStudioTab resize works")
        
        hair_studio_tab.close()
        logger.info("✓ HairStudioTab functionality tested successfully")
        return True
        
    except Exception as e:
        logger.error(f"✗ HairStudioTab test failed: {e}")
        logger.error(traceback.format_exc())
        return False

def test_drag_drop_functionality():
    """Test drag and drop functionality"""
    logger = logging.getLogger(__name__)
    logger.info("Testing drag-drop functionality...")
    
    try:
        from qtpy import QtWidgets, QtCore, QtTest, QtGui
        from qtpy.QtCore import Qt, QPoint, QMimeData
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_item import AssetItem
        from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Create application
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Create test window
        test_window = QtWidgets.QMainWindow()
        test_window.setWindowTitle("Drag-Drop Test")
        test_window.setGeometry(100, 100, 800, 600)
        
        central_widget = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout(central_widget)
        
        # Create hair manager
        hair_manager = HairManager()
        
        # Create asset item (source)
        test_asset = {
            "id": "test_asset_1",
            "name": "Test Hair Asset",
            "asset_type": "card",
            "thumbnail": None,
            "file_path": "test/path/asset.ma"
        }
        
        asset_item = AssetItem(test_asset, hair_manager=hair_manager)
        
        # Create component list (target)
        component_list = ComponentList(hair_manager=hair_manager)
        
        # Add to layout
        layout.addWidget(asset_item)
        layout.addWidget(component_list)
        
        test_window.setCentralWidget(central_widget)
        test_window.show()
        app.processEvents()
        
        logger.info("✓ Drag-drop test window created")
        
        # Test drag start
        logger.info("Testing drag start...")
        
        # Simulate mouse press on asset item
        press_pos = asset_item.rect().center()
        press_event = QtGui.QMouseEvent(
            QtCore.QEvent.MouseButtonPress,
            press_pos,
            Qt.LeftButton,
            Qt.LeftButton,
            Qt.NoModifier
        )
        asset_item.mousePressEvent(press_event)
        app.processEvents()
        
        logger.info("✓ Mouse press event simulated")
        
        # Test drag data creation
        if hasattr(asset_item, 'startDrag'):
            logger.info("✓ AssetItem has startDrag method")
        
        # Test drop acceptance
        logger.info("Testing drop acceptance...")
        
        # Create mime data
        mime_data = QMimeData()
        mime_data.setText("test_asset_1")
        
        # Simulate drag enter on component list
        drag_enter_event = QtGui.QDragEnterEvent(
            component_list.rect().center(),
            Qt.CopyAction,
            mime_data,
            Qt.LeftButton,
            Qt.NoModifier
        )
        
        if hasattr(component_list, 'dragEnterEvent'):
            component_list.dragEnterEvent(drag_enter_event)
            logger.info("✓ ComponentList drag enter event handled")
        
        # Test actual drop
        logger.info("Testing drop operation...")
        
        drop_event = QtGui.QDropEvent(
            component_list.rect().center(),
            Qt.CopyAction,
            mime_data,
            Qt.LeftButton,
            Qt.NoModifier
        )
        
        if hasattr(component_list, 'dropEvent'):
            component_list.dropEvent(drop_event)
            logger.info("✓ ComponentList drop event handled")
        
        app.processEvents()
        
        # Verify component was added
        components = component_list.get_components()
        if len(components) > 0:
            logger.info(f"✓ Component added successfully: {components[0].get('name', 'Unknown')}")
        else:
            logger.warning("⚠ No component was added after drop")
        
        test_window.close()
        logger.info("✓ Drag-drop functionality tested")
        return True
        
    except Exception as e:
        logger.error(f"✗ Drag-drop test failed: {e}")
        logger.error(traceback.format_exc())
        return False

def run_comprehensive_tests():
    """Run all comprehensive tests"""
    logger = setup_logging()
    logger.info("=" * 60)
    logger.info("Hair Studio Comprehensive Test Suite")
    logger.info("=" * 60)
    
    # Setup environment
    setup_mock_modules()
    
    test_results = []
    
    # Test UI components
    logger.info("\n" + "=" * 40)
    logger.info("Testing UI Components")
    logger.info("=" * 40)
    result1 = test_ui_components()
    test_results.append(("UI Components", result1))
    
    # Test HairStudioTab
    logger.info("\n" + "=" * 40)
    logger.info("Testing HairStudioTab")
    logger.info("=" * 40)
    result2 = test_hair_studio_tab()
    test_results.append(("HairStudioTab", result2))
    
    # Test drag-drop functionality
    logger.info("\n" + "=" * 40)
    logger.info("Testing Drag-Drop Functionality")
    logger.info("=" * 40)
    result3 = test_drag_drop_functionality()
    test_results.append(("Drag-Drop", result3))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("Test Results Summary")
    logger.info("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✓ PASSED" if result else "✗ FAILED"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 All tests passed! Hair Studio UI is working correctly.")
    else:
        logger.info("\n⚠ Some tests failed. Please check the logs above.")
    
    return all_passed

def main():
    """Main function"""
    try:
        success = run_comprehensive_tests()
        if success:
            print("\n✓ Hair Studio comprehensive tests completed successfully")
            return 0
        else:
            print("\n✗ Some Hair Studio tests failed")
            return 1
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        return 1
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
